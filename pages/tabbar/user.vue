<template>
  <view class="user-area">
    <view class="header-area padding-lr-sm padding-top">
      <view class="dflex-b">
        <view class="member-area padding-top-sm margin-bottom dflex pos-r" @click="toProfile">
          <view>
            <image class="headimg border-radius-c"
                   :src="member.avatar || '/static/images/logo-mini.png'"></image>
          </view>
          <view class="margin-left-sm">
            <view class="info-box">
              <text class="fs-lg">{{ member.nickname || member.username || '优选用户' }}</text>
            </view>
            <view v-if="member.phone">
              <text class="fs-xxs">{{ member.phone }}</text>
            </view>
          </view>
        </view>
        <!--<view v-if="false" class="border-radius-big bg-base dflex-c padding-lr"-->
        <!--      @click="to('/pages/user/integral/sign')">-->
        <!--  <view class="iconfont fs-xl iconqiandao margin-right-xs"></view>-->
        <!--  <view>签到</view>-->
        <!--</view>-->
      </view>

      <view class="border-radius">
        <view class="text-white pos-r padding-lr padding-tb-sm bg-mint">
          <view class="flex items-center">
            <text class="iconfont iconhuiyuan"></text>
            <text class="margin-left-sm">未来优选 会员</text>
          </view>
        </view>

        <view class="stats-area dflex-c bg-main">
          <view class="item dflex dflex-flow-c" @click="toOrder()">
            <text class="num">{{ userOrderAmount || 0 }}</text>
            <text>累计消费</text>
            <view class="vertical-line"></view>
          </view>
          <view class="item dflex dflex-flow-c" @click="toCoupon">
            <text class="num">{{ userCouponCount || 0 }}</text>
            <text>优惠券</text>
          </view>
        </view>
      </view>
    </view>

    <view class="container-area padding-lr-sm padding-bottom-sm">
      <!-- 我的订单 -->
      <view class="border-radius margin-top-sm bg-main">
        <use-list-title title="我的订单" iconfont="icondingdan" color="#428686" fwt="600" tip="查看全部订单"
                        @goto="toOrder('/pages/user/order/order', '全部')"></use-list-title>

        <view class="order-area padding-bottom-sm padding-lr dflex-c">
          <view class="item dflex dflex-flow-c" @click="toOrder()">
            <view class="iconfont">&#xe6da;<view class="badge badge-small"
                                                 v-if="stats && stats.order_state && stats.order_state['待付款'] > 0">
              {{ stats.order_state['待付款'] }}
            </view>
            </view>
            <text>待付款</text>
          </view>
          <view class="item dflex dflex-flow-c" @click="toOrder('/pages/user/order/order', '待发货')">
            <view class="iconfont">&#xe6d9;<view class="badge badge-small"
                                                 v-if="stats && stats.order_state && stats.order_state['待发货'] > 0">
              {{ stats.order_state['待发货'] }}
            </view>
            </view>
            <text>待发货</text>
          </view>
          <view class="item dflex dflex-flow-c" @click="toOrder('/pages/user/order/order', '待收货')">
            <view class="iconfont">&#xe6d7;<view class="badge badge-small"
                                                 v-if="stats && stats.order_state && stats.order_state['待收货'] > 0">
              {{ stats.order_state['待收货'] }}
            </view>
            </view>
            <text>待收货</text>
          </view>
          <view class="item dflex dflex-flow-c" @click="toOrder('/pages/user/order/order', '待评价')">
            <view class="iconfont">&#xe6db;<view class="badge badge-small"
                                                 v-if="stats && stats.order_state && stats.order_state['待评价'] > 0">
              {{ stats.order_state['待评价'] }}
            </view>
            </view>
            <text>待评价</text>
          </view>
          <view class="item dflex dflex-flow-c" @click="toOrder('/pages/user/order/order', '售后中')">
            <view class="iconfont">&#xe715;<view class="badge badge-small"
                                                 v-if="stats && stats.order_state && stats.order_state['售后中'] > 0">
              {{ stats.order_state['售后中'] }}
            </view>
            </view>
            <text>售后/退款</text>
          </view>
        </view>
      </view>

      <view class="border-radius margin-top-sm bg-main">
        <!-- 我的足迹 -->
        <use-list-title title="我的足迹" iconfont="iconzuji" color="#864286" fwt="600"
                        :tip="footprintCount"></use-list-title>
        <scroll-view scroll-x class="browsing-area padding-lr">
          <view class="dflex">
            <view v-for="(item, index) in footprintList" :key="index">
              <image class="border-radius-sm margin-right-sm" @click="toGoods(item.id)" :src="item.image"
                     mode="aspectFill"></image>
            </view>
          </view>
        </scroll-view>

        <use-list-title title="我的收藏" iconfont="iconshoucang-" color="#428664" fwt="600" :tip="favoriteCount"
                        @click="toFavorite"></use-list-title>
        <!--<use-list-title title="分销中心" iconfont="iconyixiaoshou" color="#864242" fwt="600" tip="分享赚钱"-->
        <!--                @click="goDistributor"></use-list-title>-->
        <use-list-title title="超级合伙人" iconfont="iconyixiaoshou" color="#864242" fwt="600" tip="加盟赚钱"
                        @click="goPartner"></use-list-title>
        <use-list-title title="收货人" iconfont="icondizhi-" color="#428642" fwt="600"
                        @click="toAddress"></use-list-title>
        <use-list-title title="设置" iconfont="iconshezhi-" color="#868642" fwt="600"
                        @click="toSetting"></use-list-title>
      </view>

      <view v-if="userStore.isLogin" class="border-radius margin-top-sm padding-sm dflex-c bg-main log-out-btn"
            @click="handleLogout">
        <text class="cell-tit">退出登录</text>
      </view>
      <view v-else class="border-radius margin-top-sm padding-sm dflex-c bg-main log-out-btn"
            @click="userStore.showLogin()">
        <text class="cell-tit">去登录</text>
      </view>

    </view>

    <use-login-form ref="loginPopup"></use-login-form>
  </view>
</template>
<script setup>
import {ref, watch} from 'vue'
import {onShow} from '@dcloudio/uni-app'

import {useUserStore} from "@/stores/user"
import {getUserInfo, deleteLogoutData} from "@/common/api";
import {getUserOrderAmountData} from "@/common/api/order"
import {getUserCouponCountData} from "@/common/api/coupon"
import {getFootprintListData, getFootprintCountData} from "@/common/api/footprint"
import {getFavoriteCountData} from "@/common/api/favorite"
import {getPartnerApplyData} from "@/common/api/partner"

import {useWxJs} from "@/common/utils";

const {share} = useWxJs()
const footprintList = ref([])

const userStore = useUserStore()
const member = ref({})
const userOrderAmount = ref(0)
const userCouponCount = ref(0)
const footprintCount = ref(0)
const favoriteCount = ref(0)
const stats = ref({})

const init = async () => {
  member.value = (await getUserInfo()).sysUser;
  userOrderAmount.value = await getUserOrderAmountData()
  userCouponCount.value = await getUserCouponCountData()
  footprintCount.value = await getFootprintCountData()
  footprintList.value = await getFootprintListData()
  favoriteCount.value = await getFavoriteCountData()
}
const handleLogout = async () => {
  await deleteLogoutData()
  userStore.logout()
  uni.showToast({title: '退出成功', icon: 'success'})
}

const toOrder = (state) => {
  uni.navigateTo({
    url: '/pages/order/order-list'
  })
}

const toGoods = (id) => {
  uni.navigateTo({
    url: '/pages/goods/goods?id=' + id
  })
}

const toAddress = () => {
  uni.navigateTo({
    url: '/pages/address/address-list'
  })
}

const toFavorite = () => {
  uni.navigateTo({
    url: '/pages/favorite/favorite'
  })
}
const goDistributor = () => {
  uni.navigateTo({
    url: '/pages/distributor/distributor'
  })
}

const goPartner = () =>{
  // 当前用户是否有申请记录

  const apply = await getPartnerApplyData()
  if (!apply) {
    uni.navigateTo({
      url: '/pages/partner/apply'
    })
    return
  }
  if (apply.status !== 2) {
    uni.navigateTo({
      url: '/pages/partner/apply-state'
    })
    return
  }
  uni.navigateTo({
    url: '/pages/partner/home'
  })

}

const toCoupon = () => {
  uni.navigateTo({
    url: '/pages/coupon/coupon'
  })
}

const toSetting = () => {
  uni.navigateTo({
    url: '/pages/setting/setting'
  })
}

const toProfile = () => {
  uni.navigateTo({
    url: '/pages/setting/profile'
  })
}

watch(() => userStore.isLogin, (newVal) => {
  if (newVal) {
    init()
  }
})

onShow(() => {
  init()
  share()
})

</script>
<style lang="scss">
page {
  min-height: 100%;
  background: $page-color-base;
}

.member-area {
  image {
    width: 130rpx;
    height: 130rpx;
    border: 5rpx solid #fff;
  }
}

.vip-card-area {
  color: #f7d680;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.8));
}

.stats-area {
  .item {
    padding: 30rpx 0;
  }

  .num {
    font-size: $font-lg;
    color: $font-color-dark;
    margin-bottom: 6rpx;
  }
}

.order-area {
  .item {
  }

  .iconfont {
    position: relative;
    font-size: $font-lg + 8upx;

    .badge {
      right: initial;
    }
  }
}

.stats-area .item,
.order-area .item {
  position: relative;
  font-size: $font-sm;
  color: $font-color-base;
  flex: 1;
}

.browsing-area {
  image {
    width: 160rpx;
    height: 160rpx;
  }
}

::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}

.log-out-btn {
  color: $font-color-base;
}
</style>
