<template>
  <view class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <view class="max-w-4xl mx-auto px-4 py-8">
      <!-- 头部标题 -->
      <view class="text-center mb-8">
        <text
          class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4 block">
          超级合伙人合作申请
        </text>
        <text class="text-gray-600 text-lg block">资源引荐官 / 超级合伙人</text>
      </view>

      <!-- 核心优势展示 -->
      <view class="bg-white rounded-2xl shadow-xl p-8 mb-8 border border-gray-100">
        <text class="text-xl font-bold text-gray-800 mb-6 text-center block">为什么选择我们？核心优势</text>
        <view class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <view v-for="(advantage, index) in coreAdvantages" :key="index"
                class="flex items-start space-x-4 p-4 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 hover:shadow-md transition-all duration-300"
                @tap="handleAdvantageClick(advantage)">
            <view class="bg-white p-3 rounded-full shadow-sm">
              <text class="text-3xl">{{ advantage.icon }}</text>
            </view>
            <view class="flex-1">
              <text class="font-semibold text-gray-800 mb-1 block">{{ advantage.title }}</text>
              <text class="text-gray-600 text-sm block">{{ advantage.desc }}</text>
            </view>
            <text class="text-green-500 text-xl">✅</text>
          </view>
        </view>
      </view>

      <!-- 主表单 -->
      <view class="space-y-8">
        <!-- 基本信息 -->
        <view class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <view class="flex items-center mb-6">
            <text class="text-2xl mr-3">👤</text>
            <text class="text-2xl font-bold text-gray-800">基本信息</text>
          </view>

          <view class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 姓名 -->
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">姓名 *</text>
              <view class="relative">
                <text class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">👤</text>
                <input
                  type="text"
                  v-model="formData.name"
                  :class="['w-full pl-12 px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all',
                                          errors.name ? 'border-red-300 bg-red-50' : 'border-gray-300']"
                  placeholder="请输入您的姓名"
                />
              </view>
              <text v-if="errors.name" class="text-red-500 text-sm mt-1 block">{{ errors.name }}</text>
            </view>

            <!-- 性别 -->
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">性别 *</text>
              <radio-group @change="(val)=>formData.gender=val.detail.value" class="flex space-x-4">
                <label class="flex ">
                  <radio
                    value="1"
                    :checked="formData.gender === '1'"
                    class="w-4 h-4 text-blue-600"
                  />
                  <text class="ml-2 text-gray-700">男</text>
                </label>
                <label class="flex ">
                  <radio
                    value="2"
                    :checked="formData.gender === '2'"
                    class="w-4 h-4 text-blue-600"
                  />
                  <text class="ml-2 text-gray-700">女</text>
                </label>
              </radio-group>
              <text v-if="errors.gender" class="text-red-500 text-sm mt-1 block">{{ errors.gender }}</text>
            </view>

            <!-- 联系电话 -->
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">联系电话 *</text>
              <view class="relative">
                <text class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">📞</text>
                <input
                  type="number"
                  v-model="formData.phone"
                  :class="['w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all',
                                              errors.phone ? 'border-red-300 bg-red-50' : 'border-gray-300']"
                  placeholder="请输入手机号"
                />
              </view>
              <text v-if="errors.phone" class="text-red-500 text-sm mt-1 block">{{ errors.phone }}</text>
            </view>

            <!-- 电子邮箱 -->
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">电子邮箱 *</text>
              <view class="relative">
                <text class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">📧</text>
                <input
                  type="text"
                  v-model="formData.email"
                  :class="['w-full pl-12 pr-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all',
                                              errors.email ? 'border-red-300 bg-red-50' : 'border-gray-300']"
                  placeholder="请输入邮箱地址"
                />
              </view>
              <text v-if="errors.email" class="text-red-500 text-sm mt-1 block">{{ errors.email }}</text>
            </view>

            <!-- 身份证号 -->
            <view class="md:col-span-2">
              <text class="block text-sm font-medium text-gray-700 mb-2">身份证号 *</text>
              <input
                type="text"
                v-model="formData.idCard"
                :class="['w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all',
                                          errors.idCard ? 'border-red-300 bg-red-50' : 'border-gray-300']"
                placeholder="请输入身份证号"
              />
              <text v-if="errors.idCard" class="text-red-500 text-sm mt-1 block">{{ errors.idCard }}</text>
            </view>

            <!-- 所在地区 -->
            <view class="md:col-span-2">
              <text class="block text-sm font-medium text-gray-700 mb-2">所在地区 *</text>
              <view
                :class="['w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all cursor-pointer flex items-center justify-between',
                         errors.province ? 'border-red-300 bg-red-50' : 'border-gray-300']"
                @tap="openCityPicker"
              >
                <text v-if="formData.province" class="text-gray-800">
                  {{ formData.province }}{{ formData.city }}{{ formData.district }}
                </text>
                <text v-else class="text-gray-400">请选择省份/城市/区县</text>
                <text class="text-gray-400 text-lg">📍</text>
              </view>
              <text v-if="errors.province" class="text-red-500 text-sm mt-1 block">{{ errors.province }}</text>
              <text class="text-gray-500 text-xs mt-1 block">注：西安、运城为总部直管区域，不可选择</text>
            </view>

            <!-- 详细地址 -->
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">详细地址</text>
              <input
                type="text"
                v-model="formData.address"
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="请输入详细地址"
              />
            </view>
          </view>
        </view>

        <!-- 合作身份选择 -->
        <view class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <view class="flex items-center mb-6">
            <text class="text-2xl mr-3">👥</text>
            <text class="text-2xl font-bold text-gray-800">合作身份选择</text>
          </view>

          <view class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <view
              v-for="level in partnerLevels"
              :key="level.id"
              :class="['p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 hover:shadow-lg',
                                      formData.partnerLevelId === level.id
                                          ? 'border-blue-500 bg-blue-50 shadow-lg'
                                          : 'border-gray-200 hover:border-gray-300']"
              @tap="selectPartnerLevel(level.id)"
            >
              <view class="flex items-center justify-between mb-4">
                <text class="text-lg font-bold text-gray-800">{{ level.name }}</text>
                <radio
                  :checked="formData.partnerLevelId === level.id"
                  class="w-5 h-5 text-blue-600"
                />
              </view>

              <view class="space-y-3">
                <view class="flex justify-between items-center">
                  <text class="text-gray-600">押金：</text>
                  <text class="font-semibold text-gray-800">
                    {{ level.deposit === 0 ? '无' : level.deposit + '元' }}
                  </text>
                </view>
                <view class="flex justify-between items-center">
                  <text class="text-gray-600">收益：</text>
                  <text class="font-semibold text-green-600">{{ level.commission }}</text>
                </view>
                <view class="pt-3 border-t border-gray-200">
                  <text class="text-sm text-gray-600 mb-2 block">特点：</text>
                  <view class="flex flex-wrap gap-2">
                    <text
                      v-for="(feature, index) in level.features"
                      :key="index"
                      class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-xs inline-block"
                    >
                      {{ feature }}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <text v-if="errors.partnerLevelId" class="text-red-500 text-sm mt-2 block">{{ errors.partnerLevelId }}</text>
        </view>

        <!-- 补充信息 -->
        <view v-if="formData.partnerLevelId" class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <view class="flex items-center mb-6">
            <text class="text-2xl mr-3">📝</text>
            <text class="text-2xl font-bold text-gray-800">补充信息</text>
          </view>

          <!-- 资源引荐官信息 -->
          <view v-if="formData.partnerLevelId === '1'">
            <text class="block text-sm font-medium text-gray-700 mb-2">
              现有可对接资源描述（如门店、企业、社群等）
            </text>
            <textarea
              v-model="formData.resourceDescription"
              class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
              placeholder="请详细描述您的资源情况"
              auto-height
            ></textarea>
          </view>

          <!-- 超级合伙人信息 -->
          <view v-else-if="formData.partnerLevelId && formData.partnerLevelId !== '1'" class="space-y-6">
            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">
                市场拓展经验（如团队管理、渠道开发等）
              </text>
              <textarea
                v-model="formData.marketExperience"
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="请描述您的市场拓展经验"
                auto-height
              ></textarea>
            </view>

            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">押金缴纳方式</text>
              <radio-group v-model="formData.depositPaymentMethod" class="flex space-x-6">
                <label class="flex ">
                  <radio
                    value="once"
                    :checked="formData.depositPaymentMethod === 'once'"
                    class="w-4 h-4 text-blue-600"
                  />
                  <text class="ml-2 text-gray-700">一次性缴纳</text>
                </label>
                <label class="flex ">
                  <radio
                    value="installment"
                    :checked="formData.depositPaymentMethod === 'installment'"
                    class="w-4 h-4 text-blue-600"
                  />
                  <text class="ml-2 text-gray-700">分期（返点抵扣）</text>
                </label>
              </radio-group>
            </view>

            <view>
              <text class="block text-sm font-medium text-gray-700 mb-2">计划覆盖区域（市级/区县）</text>
              <input
                type="text"
                v-model="formData.proxyArea"
                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                placeholder="请输入您计划覆盖的区域"
              />
            </view>

            <!-- 选中级别信息显示 -->
            <view v-if="selectedLevel" class="bg-blue-50 p-6 rounded-xl border border-blue-200">
              <text class="font-semibold text-blue-800 mb-3 block">您选择的级别信息：</text>
              <view class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <view>
                  <text class="text-blue-600">级别：</text>
                  <text class="font-medium text-blue-800">{{ selectedLevel.name }}</text>
                </view>
                <view>
                  <text class="text-blue-600">押金：</text>
                  <text class="font-medium text-blue-800">{{ selectedLevel.deposit }}元</text>
                </view>
                <view>
                  <text class="text-blue-600">收益：</text>
                  <text class="font-medium text-blue-800">{{ selectedLevel.commission }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 合作承诺 -->
        <view class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
          <view class="flex items-center mb-6">
            <text class="text-2xl mr-3">💳</text>
            <text class="text-2xl font-bold text-gray-800">合作承诺</text>
          </view>

          <view class="space-y-4 text-sm text-gray-700 mb-6">
            <text class="font-medium block">本人已仔细阅读《未来优选合作模式方案》，清楚了解所选合作模式的规则，并承诺：
            </text>

            <view class="space-y-3 pl-4">
              <text class="block">1.
                严格遵守全国统一客户价格体系（以黑泥精华按摩霜为例：终端价3.98元/支，按进货量享受对应折扣），不私自调价；
              </text>
              <text class="block">2. 本人同意在通过申请后，与总部签订正式合作协议，明确具体返点、价格及保密条款；</text>
              <text class="block">3. 认可"西安、运城为总部直管区域"限制，不向该区域客户销售；</text>
              <text class="block">4. 大客户（折扣低于六二折等）需提前24小时系统报备，未报备订单自愿放弃收益；</text>
              <text class="block">5. 尊重客户归属规则，不抢单、不低价挖取其他合作方客户，违规自愿承担10万元赔偿；</text>
              <text class="block">6. 资源引荐官：仅享受首次成交分成，客户后续复购与己无关；</text>
              <text class="block">7.
                超级合伙人：市场开拓费用自理，认可"合作满1年无违约全额退押金，中途退出扣除对应资料费"300元。
              </text>
            </view>
          </view>

          <view class="flex items-start space-x-3 mb-6">
            <label>
              <checkbox
                v-model="formData.agreed"
                :class="['w-5 h-5 text-blue-600 mt-1',
                                      errors.agreed ? 'border-red-300' : 'border-gray-300']"
              />
              <text class="text-gray-700 text-sm cursor-pointer" @tap="toggleAgreed">
                我已仔细阅读并同意以上所有条款，承诺严格遵守合作规则
              </text>
            </label>
          </view>
          <text v-if="errors.agreed" class="text-red-500 text-sm block">{{ errors.agreed }}</text>
        </view>

        <!-- 联系信息 -->
        <view class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl shadow-xl p-8 text-white">
          <text class="text-2xl font-bold mb-6 text-center block">联系方式</text>
          <view class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <view>
              <text class="font-semibold mb-2 block">咨询热线</text>
              <text class="block">13296653558</text>
              <text class="text-blue-200 block">工作日 9:00-18:00</text>
            </view>
            <view>
              <text class="font-semibold mb-2 block">咨询邮箱</text>
              <text class="block"><EMAIL></text>
            </view>
            <view class="md:col-span-2">
              <text class="font-semibold mb-2 block">联系地址</text>
              <text class="block">安徽省合肥市肥东县肥东新城开发区临泉东路与护城路爱就爱工业园区12栋</text>
              <text class="block">安徽合肥喜运来家具有限公司四楼办公室</text>
              <text class="block">介先生 13618687676</text>
            </view>
          </view>
          <text class="text-center text-blue-200 mt-4 block">
            提交后3个工作日内，招商专员将与您联系，同步审核结果及下一步合作流程！
          </text>
        </view>

        <!-- 提交按钮 -->
        <view class="text-center">
          <button
            @tap="handleSubmit"
            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-12 py-4 rounded-xl text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-blue-300"
          >
            提交申请
          </button>
          <text class="text-gray-500 text-sm mt-4 block">
            点击提交即表示您同意我们的服务条款和隐私政策
          </text>
        </view>
      </view>
    </view>

    <!-- 地址选择器 -->
    <use-address ref="useAddressPopup" @onConfirm="handleCityConfirm" cancelColor="#bbb" themeColor="#4f46e5">
    </use-address>
  </view>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerApplyData, putPartnerApplyData} from "@/common/api/partner"

// 地址选择器引用
const useAddressPopup = ref(null)

const formData = ref({
  name: '',
  gender: '',
  phone: '',
  email: '',
  idCard: '',
  province: '',
  city: '',
  district: '',
  address: '',
  partnerType: '',
  partnerLevelId: '',
  resourceDescription: '',
  marketExperience: '',
  depositPaymentMethod: '',
  proxyArea: '',
  agreed: false
});
// 错误信息
const errors = ref({});
// 合伙人级别配置
const partnerLevels = ref([
  {
    id: '1',
    name: '资源引荐官',
    deposit: 0,
    commission: '最高15%（一次性）',
    features: ['无门槛', '一次性分成', '仅对接资源']
  },
  {
    id: '2',
    name: 'T1级超级合伙人',
    deposit: 5000,
    commission: '9%（持续）',
    features: ['5000元押金', '持续性分成', '兼职拓展']
  },
  {
    id: '3',
    name: 'T2级超级合伙人',
    deposit: 10000,
    commission: '12%（持续）',
    features: ['10000元押金', '持续性分成', '全职运营']
  },
  {
    id: '4',
    name: 'T3级超级合伙人',
    deposit: 50000,
    commission: '15%（持续）+ 年度奖励',
    features: ['50000元押金', '持续性分成', '团队化运作']
  }
]);
// 核心优势
const coreAdvantages = ref([
  {title: '零风险起步', desc: '无需囤货，客户下单总部直发，无库存压力', icon: '🛡️'},
  {title: '全国市场任选', desc: '除西安、运城总部直管外，全国区域可自由开发', icon: '📍'},
  {title: '低门槛加入', desc: '0元即可启动，级别越高权益越丰厚', icon: '📈'},
  {title: '总部全程护航', desc: '提供培训、谈单资料、客户线索支持', icon: '👥'}
]);

// 选中的级别
const selectedLevel = computed(() => {
  return partnerLevels.value.find(level => level.id === formData.value.partnerLevelId);
});

// 处理优势点击事件（uniapp兼容性）
const handleAdvantageClick = (advantage) => {
  // 可以添加点击反馈或其他交互
  console.log('点击优势：', advantage.title);
};

// 切换同意状态
const toggleAgreed = () => {
  formData.value.agreed = !formData.value.agreed;
  if (errors.value.agreed) {
    delete errors.value.agreed;
  }
};

// 选择合伙人级别
const selectPartnerLevel = (levelId) => {
  formData.value.partnerLevelId = levelId;
  formData.value.partnerType = levelId === '1' ? 'referrer' : 'partner';
  if (errors.value.partnerLevelId) {
    delete errors.value.partnerLevelId;
  }
};

// 打开地址选择器
const openCityPicker = () => {
  useAddressPopup.value.open();
};

// 处理地址选择确认
const handleCityConfirm = (res) => {
  if (res.labelArr && res.labelArr.length > 2) {
    const province = res.labelArr[0];
    const city = res.labelArr[1];
    const district = res.labelArr[2];

    // 检查是否选择了西安或运城（总部直管区域）
    if ((province === '陕西省' && city === '西安市') ||
        (province === '山西省' && city === '运城市')) {
      uni.showToast({
        title: '西安、运城为总部直管区域，请选择其他地区',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    formData.value.province = province;
    formData.value.city = city;
    formData.value.district = district;

    // 清除相关错误信息
    if (errors.value.province) {
      delete errors.value.province;
    }
    if (errors.value.city) {
      delete errors.value.city;
    }
  }
};

// 表单验证
const validateForm = () => {
  const newErrors = {};

  if (!formData.value.name.trim()) newErrors.name = '请填写姓名';
  if (!formData.value.gender) newErrors.gender = '请选择性别';
  if (!formData.value.phone.trim()) newErrors.phone = '请填写手机号';
  else if (!/^1[3-9]\d{9}$/.test(formData.value.phone)) newErrors.phone = '请填写正确的手机号';
  if (!formData.value.email.trim()) newErrors.email = '请填写邮箱';
  else if (!/\S+@\S+\.\S+/.test(formData.value.email)) newErrors.email = '请填写正确的邮箱格式';
  if (!formData.value.idCard.trim()) newErrors.idCard = '请填写身份证号';
  else if (!/^\d{17}[\dX]$/.test(formData.value.idCard)) newErrors.idCard = '请填写正确的身份证号';
  if (!formData.value.province.trim()) newErrors.province = '请选择所在地区';
  if (!formData.value.partnerLevelId) newErrors.partnerLevelId = '请选择合作身份';
  if (!formData.value.agreed) newErrors.agreed = '请阅读并同意合作承诺';

  errors.value = newErrors;
  return Object.keys(newErrors).length === 0;
};

// 初始化表单数据
const initFormData = async () => {
  try {
    const data = await getPartnerApplyData();
    if (data) {
      // 如果有返回数据，说明用户之前已经填写过申请表，填充表单
      Object.keys(formData.value).forEach(key => {
        if (data[key] !== undefined) {
          formData.value[key] = data[key];
        }
      });
    }
  } catch (error) {
    console.log('获取申请数据失败：', error);
    // 如果获取失败，可能是用户第一次填写，继续使用默认空值
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!validateForm()) {
    // 显示验证错误
    uni.showToast({
      title: '请完善表单信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  try {
    // 显示加载提示
    uni.showLoading({
      title: '提交中...'
    });

    // 准备提交的数据
    const submitData = {
      name: formData.value.name,
      gender: formData.value.gender,
      phone: formData.value.phone,
      email: formData.value.email,
      idCard: formData.value.idCard,
      province: formData.value.province,
      city: formData.value.city,
      district: formData.value.district,
      address: formData.value.address,
      partnerType: formData.value.partnerType,
      partnerLevelId: formData.value.partnerLevelId,
      resourceDescription: formData.value.resourceDescription,
      marketExperience: formData.value.marketExperience,
      depositPaymentMethod: formData.value.depositPaymentMethod,
      proxyArea: formData.value.proxyArea,
      agreed: formData.value.agreed
    };

    console.log('提交表单数据：', submitData);

    // 调用API提交数据
    await putPartnerApplyData(submitData);

    // 隐藏加载提示
    uni.hideLoading();

    // 显示成功提示
    uni.showToast({
      title: '申请提交成功！我们将在3个工作日内与您联系。',
      icon: 'success',
      duration: 3000
    });

    // 可选：提交成功后跳转到其他页面或清空表单
    // uni.navigateBack(); // 返回上一页

  } catch (error) {
    // 隐藏加载提示
    uni.hideLoading();

    console.error('提交申请失败：', error);

    // 显示错误提示
    uni.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'none',
      duration: 2000
    });
  }
};

// 页面生命周期
onMounted(() => {
  initFormData();
});

onShow(() => {
  // 页面显示时也可以重新初始化数据
  initFormData();
});
</script>


<style scoped lang="scss">
/* uniapp 兼容性样式 */

/* 修复小程序中 input 样式问题 */
input {
  box-sizing: border-box;
  outline: none;
}

/* 修复小程序中 textarea 样式问题 */
textarea {
  box-sizing: border-box;
  outline: none;
  resize: none;
}

/* 修复小程序中 radio 和 checkbox 样式 */
radio, checkbox {
  transform: scale(0.8);
}

/* 修复小程序中按钮样式 */
button {
  border: none;
  outline: none;
}

button::after {
  border: none;
}

/* 修复小程序中渐变背景可能不支持的问题 */
.bg-gradient-fallback {
  background: #4f46e5; /* 蓝色备用色 */
}

/* 修复小程序中 transform 兼容性 */
.transform-fix {
  -webkit-transform: translateY(-1px);
  transform: translateY(-1px);
}

/* 修复小程序中阴影效果 */
.shadow-fix {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 修复小程序中圆角边框 */
.rounded-fix {
  border-radius: 12px;
  overflow: hidden;
}

/* 修复小程序中文字渐变可能不支持的问题 */
.text-gradient-fallback {
  color: #4f46e5;
}

/* 小程序特有的安全区域适配 */
.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 修复小程序中 hover 效果 */
.hover-effect:hover {
  opacity: 0.8;
  transition: opacity 0.2s;
}

/* 修复小程序中 grid 布局兼容性 */
@media screen and (max-width: 768px) {
  .grid {
    display: flex;
    flex-direction: column;
  }

  .grid-cols-2 > * {
    width: 100%;
    margin-bottom: 1rem;
  }
}

/* 修复小程序中字体大小单位 */
.text-responsive {
  font-size: 32rpx;
}

@media screen and (min-width: 768px) {
  .text-responsive {
    font-size: 16px;
  }
}
</style>
