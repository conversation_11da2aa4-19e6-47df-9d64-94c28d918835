<template>
  <view>
    <!--本页面为apply操作的下一步，样式视觉风格与pages/partner/apply.vue保持统一-->

    <!--显示申请提交成功-->

    <!--显示申请审核状态-->

    <!--显示已缴纳押金金额-->

    <!--缴纳押金按钮-->
    <!--返回修改申请按钮-->
  </view>
</template>

<script setup lang="ts">
import {ref, watch} from 'vue'
import {onShow} from '@dcloudio/uni-app'
import {getPartnerApplyData} from "@/common/api/partner"

const init = async () => {
  apply.value = await getPartnerApplyData()
}

onShow(() => {
  init()
  share()
})
const apply = ref({})

</script>


<style scoped lang="scss">

</style>
