'use strict';

let dev = 'http://127.0.0.1:10081/api/'
let prod = 'https://www.weilaiyouxuan.cn/api/' //生产

// dev = prod

const baseCDNUrl = 'https://pangu.file.muzuhui.com'
const tenantID = 1
const clientID = 'yxmall:yxmall'
const passwordEncKey = 'pigxpigxpigxpigx'


let config = {
	debug: !0,
	version: '1.0.9.241219',
	
	route: {
		home: '/pages/tabbar/home',
		login: '/pages/login/login',
		order: '/pages/user/order/order',
		pay: '/pages/pay/pay',
		search: '/pages/home/<USER>/search',
		goods: '/pages/goods/goods',
		goodslist: '/pages/goods/goods-list'
	},
	provider: '',
	provider_names: {
		'weixin': '微信',
		'qq': 'QQ',
		'alipay': '支付宝',
		'baidu': '百度',
		'toutiao': '头条',
	},
	
	const: {
		__app: '__app',
		__member: '__member',
		__access_token: 'USE_ACCESS_TOKEN'
	},
	baseHttpUrl: process.env.NODE_ENV === 'development' ? dev : prod,
	baseCDNUrl,
	tenantID,
	clientID,
	passwordEncKey,
};

export default config
