import * as CryptoJS from 'crypto-js'
import wxJs from 'weixin-js-sdk'
import {getWxJsConfigData} from '@/common/api/wechat'
import {getShareCodeData} from "@/common/api/distribution";
import {onLoad} from '@dcloudio/uni-app'
import {useUserStore} from "@/stores/user";

/**
 *加密处理
 */
export function encryption (src, keyWord) {
	const key = CryptoJS.enc.Utf8.parse(keyWord);
	// 加密
	const encrypted = CryptoJS.AES.encrypt(src, key, {
		iv: key,
		mode: CryptoJS.mode.CFB,
		padding: CryptoJS.pad.NoPadding,
	});
	return encrypted.toString();
}

/**
 *加密处理
 */
export function encryptionBase64 (rawStr) {
	let wordArray = CryptoJS.enc.Utf8.parse(rawStr);
	return CryptoJS.enc.Base64.stringify(wordArray);
}


export function useWxJs () {
	const getJSConfig = async () => {
		const res = await getWxJsConfigData()
		const config = {...res, jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData', 'scanQRCode']}
		wxJs.config(config)
	}
	
	const share = async (title, img, desc, url) => {
		
		await getJSConfig()
		
		const shareCode = uni.getStorageSync('shareCode')
		url = url || location.href
		const urlObj = new URL(url)
		if (shareCode) {
			// 先分析url格式，在url中追加shareCode参数
			urlObj.searchParams.set('share_code', shareCode)
		}
		urlObj.searchParams.set('t', new Date().getTime() + '')
		url = urlObj.toString()
		
		wxJs.ready(() => {
			wxJs.updateAppMessageShareData({
				title: title || '未来优选 为爱代盐', // 分享标题
				desc: desc || '邂逅中国死海最美盐泥，洞悉沐足行业破局与焕新。', // 分享描述
				link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: img || 'https://pangu.file.muzuhui.com/youxuan/erp/17430648346851741071139888pro2.png', // 分享图标
				success: function () {
					console.log("设置成功")
					// 设置成功
				},
				fail: function (res) {
					console.log("设置失败", res)
				}
			})
			wxJs.updateTimelineShareData({
				title: title || '未来优选 为爱代盐', // 分享标题
				desc: desc || '邂逅中国死海最美盐泥，洞悉沐足行业破局与焕新。', // 分享描述
				link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: img || 'https://pangu.file.muzuhui.com/youxuan/erp/17430648346851741071139888pro2.png', // 分享图标
				success: function () {
					console.log("设置成功")
					// 设置成功
				},
				fail: function (res) {
					console.log("设置失败", res)
				}
			})
		})
	}
	
	const scan = async () => {
		await getJSConfig()
		wxJs.ready(() => {
			wxJs.scanQRCode()
		})
	}
	
	// 所有分享出去的页面，同时也是用户打开的页面
	onLoad((params) => {
		if (params.share_code) {
			uni.setStorageSync('fromShareCode', params.share_code)
		}
		if(useUserStore().isLogin){
			getShareCodeData(params.share_code).then(res=>{
				uni.setStorageSync('shareCode', res)
			})
		}
	})
	
	
	return {share, scan}
}
