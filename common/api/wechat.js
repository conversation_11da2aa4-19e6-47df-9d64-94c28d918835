import http from "@/common/http"

export const getAuthUrlData = (params) => {
	return http.get('/mall/client/auth/wx_url', {params, header: {skipToken: '1'}})
}
export const getOpenidData = (code) => {
	return http.get('/mall/client/auth/wx_openid/' + code, {header: {skipToken: '1'}})
}
export const getPrePayOrderData = (params) => {
	return http.get('/mall/client/pay/pre_order', {params})
}
export const getWxJsConfigData = () => {
	const url = encodeURIComponent(location.href.split("#")[0])
	return http.get('/mall/client/auth/wx_js_config', {params: {url}, header: {skipToken: '1'}})
}
